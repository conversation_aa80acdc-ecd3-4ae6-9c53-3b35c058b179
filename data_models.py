"""
Data Models and Constants for Psychiatric Assessment System

This module contains all data structures, validation functions, and constants
used throughout the psychiatric assessment application.
"""

import datetime
import json
from typing import Dict, Any, List
import streamlit as st

# Enhanced Data Structures for DSM-5 Substance Categories and Psychiatric Medications

# DSM-5 Substance Categories with specific substances and assessment fields
DSM5_SUBSTANCE_CATEGORIES = {
    "Alcohol": {
        "substances": ["Beer", "Wine", "Spirits", "Other alcoholic beverages"],
        "routes": ["Oral"],
        "assessment_fields": ["frequency", "quantity", "binge_episodes", "withdrawal", "tolerance"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Cannabis": {
        "substances": ["Marijuana", "Hash", "Edibles", "Concentrates", "Synthetic cannabis (K2/Spice)"],
        "routes": ["Smoking", "Vaping", "Oral", "Other"],
        "assessment_fields": ["frequency", "method_use", "potency", "withdrawal", "tolerance"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Stimulants": {
        "substances": ["Cocaine", "Crack cocaine", "Methamphetamine", "Amphetamines", "ADHD medications (misused)", "Synthetic stimulants"],
        "routes": ["Nasal", "IV", "Smoking", "Oral", "Other"],
        "assessment_fields": ["frequency", "route", "withdrawal", "tolerance", "psychotic_symptoms"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Opioids": {
        "substances": ["Heroin", "Prescription opioids", "Fentanyl", "Oxycodone", "Hydrocodone", "Morphine", "Synthetic opioids"],
        "routes": ["IV", "Nasal", "Smoking", "Oral", "Other"],
        "assessment_fields": ["frequency", "route", "withdrawal", "tolerance", "overdose_history"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Sedatives_Hypnotics_Anxiolytics": {
        "substances": ["Benzodiazepines", "Barbiturates", "Sleep medications", "Muscle relaxants"],
        "routes": ["Oral", "IV", "Other"],
        "assessment_fields": ["frequency", "prescribed_vs_illicit", "withdrawal", "tolerance"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Hallucinogens": {
        "substances": ["LSD", "Psilocybin mushrooms", "PCP", "MDMA/Ecstasy", "Ketamine", "Synthetic hallucinogens"],
        "routes": ["Oral", "Nasal", "IV", "Other"],
        "assessment_fields": ["frequency", "bad_trips", "flashbacks", "tolerance"],
        "dsm5_criteria": [
            "Tolerance", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use", "Flashbacks or persistent perceptual disturbances"
        ]
    },
    "Inhalants": {
        "substances": ["Solvents", "Aerosols", "Gases", "Nitrites"],
        "routes": ["Inhalation"],
        "assessment_fields": ["frequency", "method", "medical_complications"],
        "dsm5_criteria": [
            "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Tobacco": {
        "substances": ["Cigarettes", "Cigars", "Pipe tobacco", "Chewing tobacco", "E-cigarettes/Vaping", "Nicotine patches/gum"],
        "routes": ["Smoking", "Vaping", "Oral", "Transdermal", "Other"],
        "assessment_fields": ["frequency", "quantity", "quit_attempts", "withdrawal"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Caffeine": {
        "substances": ["Coffee", "Tea", "Energy drinks", "Caffeine pills", "Soft drinks"],
        "routes": ["Oral"],
        "assessment_fields": ["daily_intake", "withdrawal_symptoms", "sleep_impact"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Continued use despite physical/psychological problems", "Craving or strong desire to use"
        ]
    },
    "Other_Unknown": {
        "substances": ["Synthetic drugs", "Unknown substances", "Other"],
        "routes": ["Oral", "Nasal", "IV", "Smoking", "Other"],
        "assessment_fields": ["frequency", "effects", "complications"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    }
}

# Comprehensive Psychiatric Medication Classes
PSYCHIATRIC_MEDICATION_CLASSES = {
    "Antidepressants": {
        "SSRIs": ["Sertraline (Zoloft)", "Fluoxetine (Prozac)", "Escitalopram (Lexapro)", "Paroxetine (Paxil)", "Citalopram (Celexa)", "Fluvoxamine (Luvox)"],
        "SNRIs": ["Venlafaxine (Effexor)", "Duloxetine (Cymbalta)", "Desvenlafaxine (Pristiq)", "Levomilnacipran (Fetzima)"],
        "Atypical": ["Bupropion (Wellbutrin)", "Mirtazapine (Remeron)", "Trazodone", "Vilazodone (Viibryd)", "Vortioxetine (Trintellix)"],
        "TCAs": ["Amitriptyline", "Nortriptyline", "Imipramine", "Desipramine", "Clomipramine", "Doxepin"],
        "MAOIs": ["Phenelzine (Nardil)", "Tranylcypromine (Parnate)", "Selegiline (Emsam)", "Isocarboxazid (Marplan)"]
    },
    "Mood_Stabilizers": {
        "Lithium": ["Lithium carbonate", "Lithium citrate"],
        "Anticonvulsants": ["Valproic acid (Depakote)", "Carbamazepine (Tegretol)", "Lamotrigine (Lamictal)", "Oxcarbazepine (Trileptal)", "Topiramate (Topamax)", "Gabapentin"]
    },
    "Antipsychotics": {
        "Atypical": ["Risperidone (Risperdal)", "Olanzapine (Zyprexa)", "Quetiapine (Seroquel)", "Aripiprazole (Abilify)", "Ziprasidone (Geodon)", "Paliperidone (Invega)", "Clozapine (Clozaril)", "Asenapine (Saphris)", "Lurasidone (Latuda)", "Brexpiprazole (Rexulti)"],
        "Typical": ["Haloperidol (Haldol)", "Chlorpromazine (Thorazine)", "Fluphenazine (Prolixin)", "Perphenazine", "Thiothixene (Navane)", "Trifluoperazine (Stelazine)"]
    },
    "Anxiolytics_Benzodiazepines": {
        "Short_acting": ["Alprazolam (Xanax)", "Lorazepam (Ativan)", "Triazolam (Halcion)", "Oxazepam (Serax)"],
        "Long_acting": ["Clonazepam (Klonopin)", "Diazepam (Valium)", "Chlordiazepoxide (Librium)", "Clorazepate (Tranxene)"]
    },
    "Stimulants": {
        "ADHD_medications": ["Methylphenidate (Ritalin)", "Amphetamine (Adderall)", "Lisdexamfetamine (Vyvanse)", "Atomoxetine (Strattera)", "Dextroamphetamine (Dexedrine)", "Guanfacine (Intuniv)", "Clonidine (Kapvay)"]
    },
    "Sleep_Medications": {
        "Z_drugs": ["Zolpidem (Ambien)", "Eszopiclone (Lunesta)", "Zaleplon (Sonata)"],
        "Other": ["Ramelteon (Rozerem)", "Suvorexant (Belsomra)", "Doxepin (Silenor)", "Melatonin"]
    },
    "Anti_anxiety_Non_Benzo": {
        "medications": ["Buspirone (Buspar)", "Hydroxyzine (Vistaril)", "Gabapentin", "Pregabalin (Lyrica)", "Propranolol"]
    },
    "Other_Psychiatric": {
        "medications": ["Memantine (Namenda)", "Donepezil (Aricept)", "Modafinil (Provigil)", "Naltrexone", "Acamprosate (Campral)", "Disulfiram (Antabuse)", "Varenicline (Chantix)"]
    }
}

# Frequency options for substance use
FREQUENCY_OPTIONS = ["Daily", "Several times per week", "Weekly", "Several times per month", "Monthly", "Occasional", "Rarely"]

# Duration options for substance use
DURATION_OPTIONS = ["Days", "Weeks", "1-6 months", "6-12 months", "1-2 years", "2-5 years", "5-10 years", "More than 10 years"]

# Route of administration options
ROUTE_OPTIONS = ["Oral", "Nasal", "IV", "Smoking", "Vaping", "Transdermal", "Other"]

# Amount/dosage units
AMOUNT_UNITS = {
    "Alcohol": ["drinks", "bottles", "glasses", "shots"],
    "Cannabis": ["grams", "joints", "bowls", "edibles"],
    "Stimulants": ["grams", "lines", "hits", "pills"],
    "Opioids": ["pills", "mg", "bags", "hits"],
    "Tobacco": ["cigarettes", "packs", "cigars", "pods"],
    "Other": ["units", "doses", "hits", "pills"]
}

# Data validation functions
def validate_substance_use_data(substance_data: Dict[str, Any]) -> List[str]:
    """Validate substance use data for consistency and completeness"""
    errors = []

    if not isinstance(substance_data, dict):
        errors.append("Substance data must be a dictionary")
        return errors

    # Check for logical consistency
    for category, data in substance_data.items():
        if category in DSM5_SUBSTANCE_CATEGORIES and isinstance(data, dict):
            # Can't have withdrawal without use
            if data.get('withdrawal') and not data.get('used'):
                errors.append(f"{category}: Cannot have withdrawal without substance use")

            # Can't have tolerance without use
            if data.get('tolerance') and not data.get('used'):
                errors.append(f"{category}: Cannot have tolerance without substance use")

            # Age onset should be reasonable
            if data.get('age_onset') and (data.get('age_onset') < 5 or data.get('age_onset') > 80):
                errors.append(f"{category}: Age of onset should be between 5 and 80")

            # Validate DSM-5 criteria count
            if 'dsm5_criteria' in data and isinstance(data['dsm5_criteria'], list):
                criteria_count = len([c for c in data['dsm5_criteria'] if c])
                if criteria_count > 11:  # Maximum DSM-5 criteria
                    errors.append(f"{category}: Too many DSM-5 criteria selected ({criteria_count}/11)")

    return errors

def validate_medication_history_data(medication_data: Dict[str, Any]) -> List[str]:
    """Validate medication history data for consistency and completeness"""
    errors = []

    if not isinstance(medication_data, dict):
        errors.append("Medication data must be a dictionary")
        return errors

    for med_class, data in medication_data.items():
        if isinstance(data, dict) and 'trials' in data:
            for i, trial in enumerate(data['trials']):
                if isinstance(trial, dict):
                    # Validate response rating
                    if 'response_rating' in trial and trial['response_rating']:
                        if not (1 <= trial['response_rating'] <= 5):
                            errors.append(f"{med_class} trial {i+1}: Response rating must be between 1 and 5")

                    # Validate adherence rating
                    if 'adherence_rating' in trial and trial['adherence_rating']:
                        if not (1 <= trial['adherence_rating'] <= 5):
                            errors.append(f"{med_class} trial {i+1}: Adherence rating must be between 1 and 5")

                    # Validate duration
                    if 'duration_weeks' in trial and trial['duration_weeks']:
                        if trial['duration_weeks'] < 0:
                            errors.append(f"{med_class} trial {i+1}: Duration cannot be negative")
                        elif trial['duration_weeks'] > 520:  # 10 years
                            errors.append(f"{med_class} trial {i+1}: Duration seems unreasonably long (>10 years)")

                    # Validate medication name is not empty
                    if 'medication' in trial and not trial['medication'].strip():
                        errors.append(f"{med_class} trial {i+1}: Medication name cannot be empty")

    return errors

def get_dsm5_severity(criteria_count: int) -> str:
    """Calculate DSM-5 substance use disorder severity based on criteria count"""
    if criteria_count >= 6:
        return "Severe"
    elif criteria_count >= 4:
        return "Moderate"
    elif criteria_count >= 2:
        return "Mild"
    else:
        return "No disorder"

def get_severity_class(severity: str) -> str:
    """Get CSS class for severity indicator"""
    severity_classes = {
        "Severe": "severity-severe",
        "Moderate": "severity-moderate",
        "Mild": "severity-mild",
        "No disorder": "severity-none"
    }
    return severity_classes.get(severity, "severity-none")

# Custom JSON encoder to handle date objects
class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles datetime objects"""
    def default(self, obj):
        if isinstance(obj, (datetime.datetime, datetime.date)):
            return obj.isoformat()
        elif isinstance(obj, datetime.time):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        return super().default(obj)

def safe_json_dumps(obj, **kwargs):
    """Safely serialize objects to JSON with custom encoder"""
    try:
        return json.dumps(obj, cls=DateTimeEncoder, **kwargs)
    except (TypeError, ValueError) as e:
        # Fallback: convert problematic objects to strings
        try:
            def convert_to_serializable(item):
                if isinstance(item, (datetime.datetime, datetime.date, datetime.time)):
                    return item.isoformat()
                elif isinstance(item, dict):
                    return {k: convert_to_serializable(v) for k, v in item.items()}
                elif isinstance(item, list):
                    return [convert_to_serializable(i) for i in item]
                elif hasattr(item, '__dict__'):
                    return str(item)
                else:
                    return item

            converted_obj = convert_to_serializable(obj)
            return json.dumps(converted_obj, **kwargs)
        except Exception as fallback_error:
            st.error(f"JSON serialization failed: {fallback_error}")
            return json.dumps({"error": "Serialization failed", "original_error": str(e)})
