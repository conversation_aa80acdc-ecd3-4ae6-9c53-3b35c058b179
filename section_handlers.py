"""
Section Handlers for Psychiatric Assessment System

This module contains handlers for individual assessment sections,
applying consistent form patterns and fixing checklist behavior issues.
"""

import streamlit as st
from typing import Dict, Any, List
from data_models import PSYCHIATRIC_MEDICATION_CLASSES
from ui_components import create_form_section, create_enhanced_multiselect, render_modern_css
from substance_section import render_substance_use_section

def render_demographics_section():
    """Render demographics section with form pattern"""
    st.markdown('<div class="section-header">👤 Demographics & Identifying Information</div>', unsafe_allow_html=True)
    st.info(f"Patient Code: **{st.session_state.patient_id}**")
    
    if 'demographics' not in st.session_state.patient_data:
        st.session_state.patient_data['demographics'] = {}
    
    demographics = st.session_state.patient_data['demographics']
    
    def demographics_form_content():
        col1, col2, col3 = st.columns(3)
        
        with col1:
            age = st.number_input("Age", min_value=0, max_value=120, value=demographics.get('age', 30))
            gender = st.selectbox(
                "Gender Identity",
                ["", "Male", "Female", "Non-binary", "Other", "Prefer not to say"],
                index=0 if not demographics.get('gender') else ["", "Male", "Female", "Non-binary", "Other", "Prefer not to say"].index(demographics.get('gender'))
            )
        
        with col2:
            marital_status = st.selectbox(
                "Marital Status",
                ["", "Single", "Married", "Divorced", "Widowed", "Separated", "Domestic partnership"],
                index=0 if not demographics.get('marital_status') else ["", "Single", "Married", "Divorced", "Widowed", "Separated", "Domestic partnership"].index(demographics.get('marital_status'))
            )
            education = st.selectbox(
                "Education Level",
                ["", "Less than high school", "High school", "Some college", "Bachelor's degree", "Graduate degree"],
                index=0 if not demographics.get('education') else ["", "Less than high school", "High school", "Some college", "Bachelor's degree", "Graduate degree"].index(demographics.get('education'))
            )
        
        with col3:
            employment = st.selectbox(
                "Employment Status",
                ["", "Employed full-time", "Employed part-time", "Unemployed", "Student", "Retired", "Disabled"],
                index=0 if not demographics.get('employment_status') else ["", "Employed full-time", "Employed part-time", "Unemployed", "Student", "Retired", "Disabled"].index(demographics.get('employment_status'))
            )
            living_situation = st.selectbox(
                "Living Situation",
                ["", "Lives alone", "Lives with family", "Lives with roommates", "Assisted living", "Homeless", "Other"],
                index=0 if not demographics.get('living_situation') else ["", "Lives alone", "Lives with family", "Lives with roommates", "Assisted living", "Homeless", "Other"].index(demographics.get('living_situation'))
            )
        
        return {
            'age': age,
            'gender': gender,
            'marital_status': marital_status,
            'education': education,
            'employment_status': employment,
            'living_situation': living_situation
        }
    
    if create_form_section(
        "Basic Demographics", 
        "demographics_form", 
        demographics_form_content,
        expanded=True,
        help_text="Collect basic demographic information for clinical context and ML training data."
    ):
        form_data = demographics_form_content()
        demographics.update(form_data)

def render_chief_complaint_section():
    """Render chief complaint section with form pattern"""
    st.markdown('<div class="section-header">📝 Chief Complaint & Referral</div>', unsafe_allow_html=True)
    
    if 'chief_complaint' not in st.session_state.patient_data:
        st.session_state.patient_data['chief_complaint'] = {}
    
    cc = st.session_state.patient_data['chief_complaint']
    
    def chief_complaint_form_content():
        presenting_problem = st.text_area(
            "Chief Complaint (in patient's own words)",
            value=cc.get('presenting_problem', ''),
            height=100,
            placeholder="What brings you here today?"
        )
        
        col1, col2 = st.columns(2)
        with col1:
            duration = st.selectbox(
                "Duration of Current Problems",
                ["", "Days", "Weeks", "1-3 months", "3-6 months", "6-12 months", "1-2 years", "More than 2 years"],
                index=0 if not cc.get('duration') else ["", "Days", "Weeks", "1-3 months", "3-6 months", "6-12 months", "1-2 years", "More than 2 years"].index(cc.get('duration'))
            )
        
        with col2:
            severity = st.selectbox(
                "Severity of Current Problems",
                ["", "Mild", "Moderate", "Severe", "Very severe"],
                index=0 if not cc.get('severity') else ["", "Mild", "Moderate", "Severe", "Very severe"].index(cc.get('severity'))
            )
        
        referral_source = st.selectbox(
            "Referral Source",
            ["", "Self-referral", "Family/friend", "Primary care physician", "Emergency department", "Court-ordered", "Other healthcare provider"],
            index=0 if not cc.get('referral_source') else ["", "Self-referral", "Family/friend", "Primary care physician", "Emergency department", "Court-ordered", "Other healthcare provider"].index(cc.get('referral_source'))
        )
        
        return {
            'presenting_problem': presenting_problem,
            'duration': duration,
            'severity': severity,
            'referral_source': referral_source
        }
    
    if create_form_section(
        "Chief Complaint Details", 
        "chief_complaint_form", 
        chief_complaint_form_content,
        expanded=True,
        help_text="Document the primary reason for the assessment and current problem severity."
    ):
        form_data = chief_complaint_form_content()
        cc.update(form_data)

def render_history_present_illness_section():
    """Render history of present illness section - the successful pattern"""
    st.markdown('<div class="section-header">📋 History of Present Illness</div>', unsafe_allow_html=True)
    
    if 'history_present_illness' not in st.session_state.patient_data:
        st.session_state.patient_data['history_present_illness'] = {}
    
    hpi = st.session_state.patient_data['history_present_illness']
    
    # Mood Disorders Form
    def mood_disorders_content():
        if 'mood_symptoms' not in hpi:
            hpi['mood_symptoms'] = {}
        
        col1, col2 = st.columns(2)
        with col1:
            depressive_symptoms = create_enhanced_multiselect(
                "Depressive Symptoms",
                ["Depressed mood", "Anhedonia", "Sleep disturbance", "Appetite changes", 
                 "Fatigue", "Concentration problems", "Guilt/worthlessness", "Psychomotor changes", "Suicidal thoughts"],
                default_values=hpi['mood_symptoms'].get('depressive_symptoms', [])
            )
        
        with col2:
            manic_symptoms = create_enhanced_multiselect(
                "Manic/Hypomanic Symptoms",
                ["Elevated mood", "Decreased need for sleep", "Grandiosity", "Racing thoughts", 
                 "Distractibility", "Increased activity", "Poor judgment", "Pressured speech"],
                default_values=hpi['mood_symptoms'].get('manic_symptoms', [])
            )
        
        return {
            'depressive_symptoms': depressive_symptoms,
            'manic_symptoms': manic_symptoms
        }
    
    if create_form_section(
        "🌙 Mood Disorders", 
        "mood_disorders_form", 
        mood_disorders_content,
        help_text="Assess for major depressive episodes, manic episodes, and mixed states."
    ):
        form_data = mood_disorders_content()
        if 'mood_symptoms' not in hpi:
            hpi['mood_symptoms'] = {}
        hpi['mood_symptoms'].update(form_data)
    
    # Anxiety Disorders Form
    def anxiety_disorders_content():
        if 'anxiety_symptoms' not in hpi:
            hpi['anxiety_symptoms'] = {}
        
        col1, col2 = st.columns(2)
        with col1:
            anxiety_symptoms = create_enhanced_multiselect(
                "Anxiety Symptoms",
                ["Excessive worry", "Restlessness", "Fatigue", "Concentration problems", 
                 "Irritability", "Muscle tension", "Sleep disturbance", "Panic attacks"],
                default_values=hpi['anxiety_symptoms'].get('anxiety_symptoms', [])
            )
        
        with col2:
            panic_symptoms = create_enhanced_multiselect(
                "Panic Attack Symptoms",
                ["Palpitations", "Sweating", "Trembling", "Shortness of breath", 
                 "Choking sensation", "Chest pain", "Nausea", "Dizziness", "Fear of dying", "Fear of losing control"],
                default_values=hpi['anxiety_symptoms'].get('panic_symptoms', [])
            )
        
        return {
            'anxiety_symptoms': anxiety_symptoms,
            'panic_symptoms': panic_symptoms
        }
    
    if create_form_section(
        "😰 Anxiety Disorders", 
        "anxiety_disorders_form", 
        anxiety_disorders_content,
        help_text="Assess for generalized anxiety disorder, panic disorder, and specific phobias."
    ):
        form_data = anxiety_disorders_content()
        if 'anxiety_symptoms' not in hpi:
            hpi['anxiety_symptoms'] = {}
        hpi['anxiety_symptoms'].update(form_data)

def render_past_psychiatric_history_section():
    """Render past psychiatric history section with fixed form patterns"""
    render_modern_css()
    
    st.markdown('<div class="section-header">🏥 Past Psychiatric History</div>', unsafe_allow_html=True)
    
    if 'past_psychiatric_history' not in st.session_state.patient_data:
        st.session_state.patient_data['past_psychiatric_history'] = {}
    
    pph = st.session_state.patient_data['past_psychiatric_history']
    
    # Previous Diagnoses Form - FIXED
    def previous_diagnoses_content():
        diagnoses = create_enhanced_multiselect(
            "Previous Psychiatric Diagnoses",
            ["Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder", "Persistent Depressive Disorder", 
             "Generalized Anxiety Disorder", "Panic Disorder", "Social Anxiety Disorder", "Specific Phobia", 
             "Obsessive-Compulsive Disorder", "PTSD", "Acute Stress Disorder", "Adjustment Disorder", 
             "Schizophrenia", "Schizoaffective Disorder", "Brief Psychotic Disorder", "Delusional Disorder", 
             "Substance Use Disorder", "Alcohol Use Disorder", "ADHD", "Autism Spectrum Disorder", 
             "Anorexia Nervosa", "Bulimia Nervosa", "Binge Eating Disorder", "Borderline Personality Disorder", 
             "Antisocial Personality Disorder", "Other Personality Disorder", "Intellectual Disability", 
             "Dementia", "Other"],
            default_values=pph.get('previous_diagnoses', []),
            help_text="Select all previous psychiatric diagnoses"
        )
        
        return {'previous_diagnoses': diagnoses}
    
    if create_form_section(
        "🏷️ Previous Psychiatric Diagnoses", 
        "previous_diagnoses_form", 
        previous_diagnoses_content,
        expanded=True,
        help_text="Document all previous psychiatric diagnoses to understand treatment history and course of illness."
    ):
        form_data = previous_diagnoses_content()
        pph.update(form_data)
    
    # Treatment History Form - FIXED
    def treatment_history_content():
        col1, col2 = st.columns(2)
        
        with col1:
            hospitalizations = st.selectbox(
                "Psychiatric Hospitalizations",
                ["None", "1 hospitalization", "2-3 hospitalizations", "4-5 hospitalizations", "More than 5 hospitalizations"],
                index=0 if not pph.get('hospitalizations') else ["None", "1 hospitalization", "2-3 hospitalizations", "4-5 hospitalizations", "More than 5 hospitalizations"].index(pph.get('hospitalizations'))
            )
            
            hospitalization_reasons = create_enhanced_multiselect(
                "Reasons for Hospitalization",
                ["Suicidal ideation/attempt", "Homicidal ideation", "Psychosis", "Severe depression", 
                 "Mania/hypomania", "Substance intoxication", "Substance withdrawal", "Self-harm", 
                 "Inability to care for self", "Medication adjustment", "Other"],
                default_values=pph.get('hospitalization_reasons', [])
            )
        
        with col2:
            outpatient_treatment = create_enhanced_multiselect(
                "Previous Outpatient Treatment",
                ["Individual therapy", "Group therapy", "Family therapy", "Couples therapy", "Medication management", 
                 "Intensive outpatient program", "Partial hospitalization", "Day treatment", "Case management", 
                 "Peer support", "Support groups", "Other"],
                default_values=pph.get('outpatient_treatment', [])
            )
            
            therapy_types = create_enhanced_multiselect(
                "Types of Therapy Received",
                ["Cognitive Behavioral Therapy (CBT)", "Dialectical Behavior Therapy (DBT)", 
                 "Psychodynamic therapy", "Interpersonal therapy", "EMDR", "Exposure therapy", 
                 "Acceptance and Commitment Therapy", "Mindfulness-based therapy", "Family therapy", 
                 "Group therapy", "Art/music therapy", "Other"],
                default_values=pph.get('therapy_types', [])
            )
        
        treatment_response = st.selectbox(
            "Overall Response to Previous Treatment",
            ["", "Excellent response", "Good response", "Partial response", "Poor response", "No response", "Mixed results"],
            index=0 if not pph.get('treatment_response') else ["", "Excellent response", "Good response", "Partial response", "Poor response", "No response", "Mixed results"].index(pph.get('treatment_response'))
        )
        
        return {
            'hospitalizations': hospitalizations,
            'hospitalization_reasons': hospitalization_reasons,
            'outpatient_treatment': outpatient_treatment,
            'therapy_types': therapy_types,
            'treatment_response': treatment_response
        }
    
    if create_form_section(
        "🏥 Treatment History", 
        "treatment_history_form", 
        treatment_history_content,
        expanded=True,
        help_text="Document previous psychiatric treatments including hospitalizations, therapy, and overall response."
    ):
        form_data = treatment_history_content()
        pph.update(form_data)
    
    # Medication History - FIXED with enhanced form pattern
    render_medication_history_section(pph)

def render_medication_history_section(pph: Dict[str, Any]):
    """Render medication history section with enhanced form patterns"""
    st.markdown("### 💊 Comprehensive Psychiatric Medication History")
    
    if 'medication_history' not in pph:
        pph['medication_history'] = {}
    
    # Medication class selection
    def medication_classes_content():
        tried_classes = create_enhanced_multiselect(
            "Medication Classes Previously Tried",
            list(PSYCHIATRIC_MEDICATION_CLASSES.keys()),
            default_values=list(pph['medication_history'].keys()),
            help_text="Select all medication classes that have been tried"
        )
        
        return {'tried_classes': tried_classes}
    
    if create_form_section(
        "💊 Medication Classes", 
        "medication_classes_form", 
        medication_classes_content,
        expanded=True,
        help_text="Select medication classes to document detailed trial history."
    ):
        form_data = medication_classes_content()
        
        # Update medication history structure
        current_classes = set(pph['medication_history'].keys())
        selected_classes = set(form_data['tried_classes'])
        
        # Add new classes
        for med_class in selected_classes - current_classes:
            pph['medication_history'][med_class] = {'trials': []}
        
        # Remove unselected classes
        for med_class in current_classes - selected_classes:
            del pph['medication_history'][med_class]
    
    # Detailed medication trials for each selected class
    for med_class in pph['medication_history'].keys():
        render_medication_class_details(med_class, pph['medication_history'][med_class])

def render_medication_class_details(med_class: str, med_data: Dict[str, Any]):
    """Render detailed medication trial information for a specific class"""
    class_display_name = med_class.replace('_', ' ')
    
    def medication_details_content():
        st.markdown(f"#### {class_display_name} Trials")
        
        # Add trial button
        if st.button(f"➕ Add {class_display_name} Trial", key=f"add_{med_class}_trial"):
            if 'trials' not in med_data:
                med_data['trials'] = []
            med_data['trials'].append({
                'medication': '',
                'response_rating': 1,
                'side_effects': [],
                'duration_weeks': 0,
                'discontinuation_reason': '',
                'adherence_rating': 1
            })
        
        # Display existing trials
        if 'trials' in med_data and med_data['trials']:
            for i, trial in enumerate(med_data['trials']):
                st.markdown(f"**Trial {i+1}:**")
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    trial['medication'] = st.text_input(
                        "Medication",
                        value=trial.get('medication', ''),
                        key=f"{med_class}_med_{i}"
                    )
                    trial['response_rating'] = st.slider(
                        "Response (1=Poor, 5=Excellent)",
                        1, 5, trial.get('response_rating', 1),
                        key=f"{med_class}_response_{i}"
                    )
                
                with col2:
                    trial['duration_weeks'] = st.number_input(
                        "Duration (weeks)",
                        min_value=0, max_value=520,
                        value=trial.get('duration_weeks', 0),
                        key=f"{med_class}_duration_{i}"
                    )
                    trial['adherence_rating'] = st.slider(
                        "Adherence (1=Poor, 5=Excellent)",
                        1, 5, trial.get('adherence_rating', 1),
                        key=f"{med_class}_adherence_{i}"
                    )
                
                with col3:
                    trial['side_effects'] = create_enhanced_multiselect(
                        "Side Effects",
                        ["Nausea", "Headache", "Dizziness", "Drowsiness", "Insomnia", 
                         "Weight gain", "Weight loss", "Sexual dysfunction", "Dry mouth", "Other"],
                        default_values=trial.get('side_effects', []),
                        key=f"{med_class}_side_effects_{i}"
                    )
                    
                    if st.button(f"🗑️ Remove Trial {i+1}", key=f"remove_{med_class}_{i}"):
                        med_data['trials'].pop(i)
                        st.rerun()
                
                st.markdown("---")
        
        # Overall class response
        overall_response = st.selectbox(
            f"Overall Response to {class_display_name}",
            ["", "Excellent", "Good", "Partial", "Poor", "No response"],
            index=0 if not med_data.get('overall_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response"].index(med_data.get('overall_response')),
            key=f"{med_class}_overall"
        )
        
        return {'overall_response': overall_response}
    
    if create_form_section(
        f"💊 {class_display_name} Details", 
        f"medication_details_{med_class}", 
        medication_details_content,
        expanded=bool(med_data.get('trials')),
        help_text=f"Document detailed trial history for {class_display_name} including response, side effects, and adherence."
    ):
        form_data = medication_details_content()
        med_data.update(form_data)
